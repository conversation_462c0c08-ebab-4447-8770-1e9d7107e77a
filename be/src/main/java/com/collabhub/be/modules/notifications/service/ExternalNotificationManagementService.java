package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.modules.notifications.converter.NotificationConverter;
import com.collabhub.be.modules.notifications.dto.NotificationPageRequest;
import com.collabhub.be.modules.notifications.dto.NotificationPageResponse;
import com.collabhub.be.modules.notifications.dto.NotificationResponse;
import com.collabhub.be.modules.notifications.exception.NotificationNotFoundException;
import com.collabhub.be.modules.notifications.repository.NotificationRepository;
import org.jooq.generated.tables.pojos.Notification;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.util.List;

/**
 * Production-grade service for managing external user notifications.
 *
 * <p>This service handles notification retrieval, status updates, and pagination for external
 * users (hub participants without user accounts) with comprehensive validation, error handling,
 * and performance optimizations. It provides feature parity with internal user notification
 * management while using email-based identification.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>Email-based notification access with security validation</li>
 *   <li>0-based pagination with {@link NotificationPageRequest}</li>
 *   <li>Bulk operations for marking notifications as read</li>
 *   <li>Comprehensive validation and error handling</li>
 *   <li>Performance-optimized queries with proper indexing</li>
 * </ul>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
@Service
@Validated
public class ExternalNotificationManagementService {

    private static final Logger logger = LoggerFactory.getLogger(ExternalNotificationManagementService.class);
    
    // Constants
    private static final String RETRIEVE_NOTIFICATIONS_MESSAGE = "Retrieving notifications for external user: {} (page={}, size={}, unreadOnly={})";
    private static final String RETRIEVED_NOTIFICATIONS_MESSAGE = "Retrieved {} notifications for external user {} (total: {})";
    private static final String MARK_READ_MESSAGE = "Marking notification as read: id={}, external user={}";
    private static final String MARK_READ_SUCCESS_MESSAGE = "Marked notification as read: id={}, external user={}";
    private static final String MARK_READ_FAILED_MESSAGE = "Failed to mark notification as read - not found or not owned: id={}, external user={}";
    private static final String MARK_ALL_READ_MESSAGE = "Marking all notifications as read for external user: {}";
    private static final String MARK_ALL_READ_SUCCESS_MESSAGE = "Marked {} notifications as read for external user {}";
    private static final String UNREAD_COUNT_MESSAGE = "Getting unread notification count for external user: {}";
    private static final String UNREAD_COUNT_RESULT_MESSAGE = "External user {} has {} unread notifications";

    private final NotificationRepository notificationRepository;
    private final NotificationConverter notificationConverter;

    public ExternalNotificationManagementService(NotificationRepository notificationRepository,
                                               NotificationConverter notificationConverter) {
        this.notificationRepository = notificationRepository;
        this.notificationConverter = notificationConverter;
    }

    /**
     * Retrieves notifications for an external user with pagination.
     *
     * <p>This method provides paginated access to notifications for external users
     * identified by email address. It uses 0-based pagination and includes total count
     * information for proper pagination controls.</p>
     *
     * @param email the email address of the external user (must be valid)
     * @param pageRequest the page request with pagination and filtering parameters (must not be null)
     * @return page of notifications with pagination metadata
     * 
     * @throws IllegalArgumentException if parameters are invalid
     */
    @Transactional(readOnly = true)
    public NotificationPageResponse getExternalUserNotifications(@NotBlank @Email String email, 
                                                               @NotNull @Valid NotificationPageRequest pageRequest) {
        
        String normalizedEmail = normalizeEmail(email);
        logRetrievalRequest(normalizedEmail, pageRequest);

        List<Notification> notifications = fetchNotifications(normalizedEmail, pageRequest);
        int totalCount = getTotalCount(normalizedEmail, pageRequest.isUnreadOnly());
        List<NotificationResponse> responseList = convertToResponseList(notifications);

        logRetrievalResult(normalizedEmail, responseList, totalCount);
        return createPageResponse(responseList, pageRequest, totalCount);
    }

    /**
     * Marks a specific notification as read for an external user.
     *
     * <p>This method marks a notification as read for the external user, ensuring that
     * users can only mark their own notifications as read. If the notification is not
     * found or doesn't belong to the user, an exception is thrown.</p>
     *
     * @param notificationId the notification ID (must be positive)
     * @param email the email address of the external user (must be valid)
     *
     * @throws IllegalArgumentException if parameters are invalid
     * @throws NotificationNotFoundException if notification is not found or not owned by user
     */
    @Transactional
    public void markNotificationAsRead(@NotNull @Positive Long notificationId, @NotBlank @Email String email) {

        String normalizedEmail = normalizeEmail(email);
        logger.debug(MARK_READ_MESSAGE, notificationId, normalizedEmail);

        boolean updated = attemptMarkAsRead(notificationId, normalizedEmail);
        handleMarkAsReadResult(updated, notificationId, normalizedEmail);
    }

    /**
     * Marks all notifications as read for an external user.
     *
     * <p>This method performs a bulk update operation to mark all unread notifications
     * as read for the external user. It returns the number of notifications that were
     * actually updated, which may be zero if all notifications were already read.</p>
     *
     * @param email the email address of the external user (must be valid)
     * @return number of notifications marked as read (non-negative)
     */
    @Transactional
    public int markAllNotificationsAsRead(@NotBlank @Email String email) {
        
        String normalizedEmail = normalizeEmail(email);
        logger.debug(MARK_ALL_READ_MESSAGE, normalizedEmail);

        int updatedCount = performBulkMarkAsRead(normalizedEmail);
        logBulkMarkAsReadResult(normalizedEmail, updatedCount);
        
        return updatedCount;
    }

    /**
     * Gets the count of unread notifications for an external user.
     *
     * <p>This method provides a fast count of unread notifications for the external user,
     * which is commonly used for displaying notification badges in the UI.</p>
     *
     * @param email the email address of the external user (must be valid)
     * @return number of unread notifications (non-negative)
     */
    @Transactional(readOnly = true)
    public int getUnreadNotificationCount(@NotBlank @Email String email) {
        
        String normalizedEmail = normalizeEmail(email);
        logger.debug(UNREAD_COUNT_MESSAGE, normalizedEmail);

        int count = getUnreadCount(normalizedEmail);
        logUnreadCountResult(normalizedEmail, count);
        
        return count;
    }

    // ========================================
    // PRIVATE HELPER METHODS
    // ========================================

    /**
     * Normalizes email address for consistent storage and querying.
     */
    private String normalizeEmail(@NotBlank String email) {
        return email.trim().toLowerCase();
    }

    /**
     * Logs the notification retrieval request.
     */
    private void logRetrievalRequest(String email, NotificationPageRequest pageRequest) {
        logger.debug(RETRIEVE_NOTIFICATIONS_MESSAGE, email, 
                    pageRequest.getPage(), pageRequest.getSize(), pageRequest.isUnreadOnly());
    }

    /**
     * Fetches notifications from the repository with pagination.
     */
    private List<Notification> fetchNotifications(String email, NotificationPageRequest pageRequest) {
        return notificationRepository.findByEmailWithPagination(
                email, pageRequest.getPage(), pageRequest.getSize(), pageRequest.isUnreadOnly());
    }

    /**
     * Gets the total count of notifications for pagination.
     */
    private int getTotalCount(String email, boolean unreadOnly) {
        return notificationRepository.countByEmail(email, unreadOnly);
    }

    /**
     * Converts notification entities to response DTOs.
     */
    private List<NotificationResponse> convertToResponseList(List<Notification> notifications) {
        return notificationConverter.toResponseList(notifications);
    }

    /**
     * Logs the notification retrieval result.
     */
    private void logRetrievalResult(String email, List<NotificationResponse> responseList, int totalCount) {
        logger.info(RETRIEVED_NOTIFICATIONS_MESSAGE, responseList.size(), email, totalCount);
    }

    /**
     * Creates the paginated response.
     */
    private NotificationPageResponse createPageResponse(List<NotificationResponse> responseList, 
                                                       NotificationPageRequest pageRequest, int totalCount) {
        return NotificationPageResponse.of(responseList, pageRequest, totalCount);
    }

    /**
     * Performs the bulk mark as read operation.
     */
    private int performBulkMarkAsRead(String email) {
        return notificationRepository.markAllAsReadByEmail(email);
    }

    /**
     * Logs the result of bulk mark as read operation.
     */
    private void logBulkMarkAsReadResult(String email, int updatedCount) {
        logger.info(MARK_ALL_READ_SUCCESS_MESSAGE, updatedCount, email);
    }

    /**
     * Gets the unread count from the repository.
     */
    private int getUnreadCount(String email) {
        return notificationRepository.countByEmail(email, true);
    }

    /**
     * Logs the unread count result.
     */
    private void logUnreadCountResult(String email, int count) {
        logger.info(UNREAD_COUNT_RESULT_MESSAGE, email, count);
    }

    /**
     * Attempts to mark a notification as read for an external user.
     *
     * @param notificationId the notification ID
     * @param email the email address
     * @return true if the notification was successfully marked as read
     */
    private boolean attemptMarkAsRead(Long notificationId, String email) {
        return notificationRepository.markAsReadByEmail(notificationId, email);
    }

    /**
     * Handles the result of marking a notification as read for an external user.
     *
     * @param updated whether the notification was successfully updated
     * @param notificationId the notification ID
     * @param email the email address
     * @throws NotificationNotFoundException if the notification was not found or not owned
     */
    private void handleMarkAsReadResult(boolean updated, Long notificationId, String email) {
        if (updated) {
            logger.info(MARK_READ_SUCCESS_MESSAGE, notificationId, email);
        } else {
            logger.warn(MARK_READ_FAILED_MESSAGE, notificationId, email);
            throw new NotificationNotFoundException("Notification not found or not owned by external user: " + notificationId);
        }
    }
}
